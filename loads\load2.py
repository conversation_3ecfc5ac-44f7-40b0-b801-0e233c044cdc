from unsloth import FastLanguageModel
from transformers import TextStreamer  # type: ignore
import torch
import torch.onnx

model, tokenizer = FastLanguageModel.from_pretrained(
    model_name="G:/00LLM/models/Qwen3-0.6B-cia-cantonese-chinese",  # 80亿参数量化模型
    max_seq_length=2048,               
    dtype = torch.float16,
    load_in_4bit=True,      
    local_files_only=True,        
)


# 导出为ONNX 
torch.onnx.export(
    model,
    (torch.ones(2, 2),),
    "model.onnx",
    input_names=["input"],  # 输入节点名称
    output_names=["output"],  # 输出节点名称
    dynamic_axes=None,  # 静态形状
    opset_version=15    # ONNX算子集版本
)

messages = [
    {"role": "user", "content": "你是谁！ 中文回答"}
]
text = tokenizer.apply_chat_template(
    messages,
    tokenize=False,
    add_generation_prompt=True,  # 生成响应必需
    enable_thinking=False,    # 禁用思考
)


_ = model.generate(
    **tokenizer(text, return_tensors="pt").to("cuda"),
    max_new_tokens=256,         # 最大生成token数
    temperature=0.6, top_p=0.95, top_k=20,
    streamer=TextStreamer(tokenizer, skip_prompt=True),
)
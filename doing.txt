http://dev.ros2.fishros.com/

https://www.oschina.net/project/tag/506/embodied-ai

https://www.pollen-robotics.com/reachy-2s-online-documentation/



docker run --rm --platform linux/amd64 -p 8888:8888 -p 6080:6080 -p 50051:50051 --name reachy2 cr.ttyuyin.com/public/reachy2:******* start_rviz:=true start_sdk_server:=true fake:=true orbbec:=false gazebo:=true


Jupyter Notebook:               http://localhost:8888/tree
VNC Viewer (RViz/Gazebo):       http://localhost:6080/vnc.html?autoconnect=1&resize=remote

http://************:6080/vnc.html?autoconnect=1&resize=remote

http://************:8888/tree




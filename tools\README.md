# Harbor Repository Client - Python 重构版本

## 概述

这是对原始 Go 版本 `repository.go` 的 Python 重构实现，提供了与 Harbor 容器镜像仓库交互的功能。

## 原始 Go 代码功能

原始的 `repository.go` 文件实现了以下功能：
- 连接到 Harbor 镜像仓库
- 删除指定项目中的镜像仓库
- 支持基本 HTTP 认证
- 处理 CSRF Token 以确保安全性
- 双重 URL 编码以处理特殊字符

## Python 重构特性

### 核心功能保持一致
- ✅ **Repository 类**: 对应 Go 版本的 `repository` struct
- ✅ **new_repository 函数**: 对应 Go 版本的 `NewRepository` 函数
- ✅ **delete 方法**: 对应 Go 版本的 `Delete` 方法
- ✅ **CICD_PROJECT 常量**: 保持相同的常量定义
- ✅ **双重 URL 编码**: 与 Go 版本完全一致的编码逻辑
- ✅ **CSRF Token 处理**: 完整的安全令牌处理流程

### Python 版本增强功能
- 🚀 **更好的错误处理**: 自定义异常类 `HarborRepositoryError`
- 🚀 **会话管理**: 使用 `requests.Session` 提高性能
- 🚀 **日志记录**: 完整的操作日志记录
- 🚀 **类型提示**: 完整的 Python 类型注解
- 🚀 **批量操作**: 新增批量删除仓库功能
- 🚀 **存在性检查**: 新增仓库存在性检查方法
- 🚀 **工具函数**: 额外的实用工具函数

## 文件结构

```
tools/
├── repository.py          # 主要的 Python 重构代码
├── test_repository.py     # 测试脚本
└── README.md             # 本文档
```

## 使用方法

### 基本用法

```python
from tools.repository import new_repository, HarborRepositoryError

# 创建客户端
client = new_repository(
    host="https://harbor.example.com",
    auth_name="admin", 
    auth_psw="password"
)

try:
    # 删除仓库
    client.delete("my-project", "my-app")
    print("删除成功")
except HarborRepositoryError as e:
    print(f"删除失败: {e}")
finally:
    client.close()
```

### 批量删除

```python
from tools.repository import delete_repository_batch

repositories = [
    {"project": "proj1", "repo": "app1"},
    {"project": "proj1", "repo": "app2"},
    {"project": "proj2", "repo": "service1"}
]

results = delete_repository_batch(
    host="https://harbor.example.com",
    auth_name="admin",
    auth_psw="password", 
    repositories=repositories
)

print(f"成功: {len(results['success'])}")
print(f"失败: {len(results['failed'])}")
print(f"未找到: {len(results['not_found'])}")
```

### 检查仓库存在性

```python
client = new_repository("https://harbor.example.com", "admin", "password")

if client.repository_exists("my-project", "my-app"):
    print("仓库存在")
    client.delete("my-project", "my-app")
else:
    print("仓库不存在")

client.close()
```

## API 对比

| Go 版本 | Python 版本 | 说明 |
|---------|-------------|------|
| `type repository struct` | `class Repository` | 仓库客户端类 |
| `NewRepository(host, authName, authPsw)` | `new_repository(host, auth_name, auth_psw)` | 创建客户端实例 |
| `(r repository) Delete(projectName, repositoryName)` | `delete(project_name, repository_name)` | 删除仓库方法 |
| `CICD_PROJECT = "devops-"` | `CICD_PROJECT = "devops-"` | 常量定义 |

## 依赖要求

```bash
pip install requests
```

## 测试

运行测试脚本：

```bash
python tools/test_repository.py
```

测试包括：
- 基本功能测试
- 批量删除测试  
- CI/CD 项目检查测试
- URL 编码测试
- 错误处理测试

## 配置说明

在使用前，请根据实际环境修改以下配置：

```python
HARBOR_HOST = "https://your-harbor-server.com"
USERNAME = "your-username"
PASSWORD = "your-password"
```

## 错误处理

Python 版本提供了更详细的错误处理：

- `HarborRepositoryError`: Harbor 操作相关错误
- `requests.RequestException`: 网络请求错误
- 详细的错误消息和日志记录

## 安全注意事项

1. **认证信息**: 不要在代码中硬编码用户名和密码
2. **HTTPS**: 建议使用 HTTPS 连接到 Harbor 服务器
3. **权限**: 确保使用的账户具有适当的删除权限
4. **日志**: 注意日志中可能包含敏感信息

## 兼容性

- Python 3.6+
- Harbor API v2.0+
- requests 库

## 贡献

如果发现问题或有改进建议，请：
1. 检查现有的测试用例
2. 添加新的测试用例（如果需要）
3. 确保与原始 Go 版本的行为一致
4. 更新文档

## 许可证

与原项目保持一致的许可证。

package harbor

import (
	"fmt"
	"net/http"
	"net/url"
)

const (
	CICD_PROJECT = "devops-"
)

type repository struct {
	Host     string
	AuthName string
	AuthPsw  string
}

func NewRepository(host, authName, authPsw string) repository {
	return repository{
		Host:     host,
		AuthName: authName,
		AuthPsw:  authPsw,
	}
}

func (r repository) Delete(projectName, repositoryName string) (err error) {
	urlPath := r.Host + "/api/v2.0/projects/" + projectName + "/repositories/" + url.PathEscape(url.PathEscape(repositoryName))
	println(urlPath)
	req, err := http.NewRequest("GET", urlPath, nil)
	if err != nil {
		return
	}
	req.SetBasicAuth(r.AuthName, r.AuthPsw)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode == http.StatusNotFound {
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("GET repository, got %s", resp.Status)
		return
	}

	reqDel, err := http.NewRequest("DELETE", urlPath, nil)
	if err != nil {
		return
	}
	reqDel.SetBasicAuth(r.AuthName, r.AuthPsw)
	reqDel.Header.Set("X-Harbor-Csrf-Token", resp.Header.Get("X-Harbor-Csrf-Token"))

	respDel, err := http.DefaultClient.Do(reqDel)
	if err != nil {
		return
	}
	defer respDel.Body.Close()
	if respDel.StatusCode != http.StatusOK {
		err = fmt.Errorf("DELETE repository, got %s", respDel.Status)
		return
	}

	return
}

//

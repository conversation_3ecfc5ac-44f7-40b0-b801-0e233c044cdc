from typing import NamedTuple

class Coordinate(NamedTuple):
    lat: float
    lon: float
    def __str__(self):
        ns = 'N' if self.lat >= 0 else 'S'
        we = 'E' if self.lon >= 0 else 'W'
        return f'{abs(self.lat):.1f}°{ns}, {abs(self.lon):.1f}°{we}'
    

class DemoPlainClass:
    a: int           
    b: float = 1.1   
    c = 'spam'    
    d: list  # Added declaration for attribute 'd'


if __name__ == '__main__':
    demo= DemoPlainClass()
    demo.a = 42
    demo.d=[3,2,1]
    print(demo.a, demo.b, demo.c, demo.d) 
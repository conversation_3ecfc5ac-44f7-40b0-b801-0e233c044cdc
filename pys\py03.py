from collections import abc
from typing import Any, Callable, TypeVar
from collections.abc import Sequence
from random import shuffle
from typing import TypeVar
T = TypeVar('T')

def make_averager():
    series = []
    def averager(new_value):
        series.append(new_value)
        total = sum(series)
        return total / len(series)
    return averager



def sample(population: Sequence[T], size: int) -> list[T]:
    if size < 1:
        raise ValueError('size must be >= 1')
    result = list(population)
    shuffle(result)
    return result[:size]


def double(x: Any) -> Any: 
    return x * 2 

def double1(x: object) -> object: 
    return x * 2 

def double2(x: abc.Sequence) -> Any: 
    return x * 2 

def double3(x: str|None=None) -> Any: 
    return x + '2' if x is not None else None
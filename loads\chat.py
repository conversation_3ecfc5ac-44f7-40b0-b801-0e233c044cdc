from transformers import AutoModelForCausalLM, AutoTokenizer

class QwenChatbot:
    def __init__(self, model_name="G:/00LLM/models/Qwen3-0.6B-cia-cantonese-chinese"):
    # def __init__(self, model_name="G:/00LLM/models/Qwen3-0.6B-cia-finetuned-002"):
    # def __init__(self, model_name="G:/00LLM/models/Qwen3-0.6B-unsloth-cia-finetuned"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name)
        self.history = [{"role": "system", "content": "你是一个普通话转为粤语的机器人。"}]

    def generate_response(self, user_input):
        messages = self.history + [{"role": "user", "content": user_input}]

        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )

        inputs = self.tokenizer(text, return_tensors="pt")
        response_ids = self.model.generate(**inputs, max_new_tokens=32768)[0][len(inputs.input_ids[0]):].tolist()
        response = self.tokenizer.decode(response_ids, skip_special_tokens=True)

        # Update history
        # self.history.append({"role": "user", "content": user_input})
        # self.history.append({"role": "assistant", "content": response})

        return response
    
# Example Usage
if __name__ == "__main__":
    chatbot = QwenChatbot()
    print("Welcome to the Qwen Chatbot!")
    print("You can ask questions, and the bot will respond.")
    print("Type 'q' to quit the chat.")
    while True:
        user_input = input("You: ")
        if user_input.lower() == 'q':
            break
        
        response = chatbot.generate_response(user_input)
        print(f"Bot: {response}")
        print("----------------------")


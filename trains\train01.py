from unsloth import FastLanguageModel
import torch
from datasets import load_dataset,Dataset
import pandas as pd
from trl import SFTTrainer, SFTConfig


model, tokenizer = FastLanguageModel.from_pretrained(
    model_name="unsloth/Qwen3-0.6B-unsloth-bnb-4bit",  # 80亿参数量化模型
    max_seq_length=2048,               # 支持2048token上下文
    # dtype = torch.float16,
    load_in_4bit=True,                             # 4位量化降低内存占用
    load_in_8bit=False,                            # 8位模式（需更高显存）
    full_finetuning=False,                         # 启用参数高效微调（PEFT）
    # token="<YOUR_HF_TOKEN>",                    # 访问权限模型需提供令牌
)

model = FastLanguageModel.get_peft_model(
    model,
    r=32,                        # LoRA矩阵秩，值越大精度越高
    target_modules=[             # 需适配的模型层
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    lora_alpha=64,               # 缩放因子，通常设为r的2倍
    lora_dropout=0,              # 关闭 dropout
    bias="none",                 # 不微调偏置项
    use_gradient_checkpointing= False,  # 支持长上下文
    random_state=3433,           # 随机种子确保可复现
)


# 加载推理与对话数据集
my_dataset = load_dataset("json", data_files="./identity.json")

 # 查看所有列名
print("数据集列名:", my_dataset.column_names)

# 标准化推理数据为对话格式
def generate_conversation(examples):
    problems = examples["instruction"]
    solutions = examples["output"]  
    return {
        "conversations": [
            {"role": "user", "content": problems},{"role": "assistant", "content": solutions}
        ]
    }

print("当前数据:", my_dataset.map(generate_conversation)["train"][0])

my_conversations = tokenizer.apply_chat_template(
    my_dataset.map(generate_conversation)["train"]["conversations"],
    tokenize=False
)

# 设定对话比例
chat_percentage = 0.7
data = pd.concat([
    pd.Series(my_conversations)
])
data.name = "text"

combined_dataset = Dataset.from_pandas(pd.DataFrame(data))
combined_dataset = combined_dataset.shuffle(seed=3407)


print("开始训练...")

trainer = SFTTrainer(
    model=model,
    tokenizer=tokenizer,
    train_dataset=combined_dataset,  # 结构化数据集
    eval_dataset=None,
    args=SFTConfig(
        dataset_text_field="text",       # 用于训练的数据集字段
        per_device_train_batch_size=2,   # 单设备训练批次大小
        gradient_accumulation_steps=4,   # 梯度累积步数
        warmup_steps=5,                  # 学习率预热步数
        max_steps=30,                    # 总训练步数
        learning_rate=2e-4,              # 学习率
        logging_steps=1,                 # 日志记录频率
        optim="adamw_8bit",              # 优化器
        weight_decay=0.01,               # 权重衰减
        lr_scheduler_type="linear",      # 学习率衰减策略
        seed=3407,
        report_to="none",                # 日志平台（可选wandb）
    ),
)

trainer_stats = trainer.train()


model.save_pretrained_merged(
    "G:/00LLM/models/Qwen3-0.6B-unsloth-cia-finetuned",
    tokenizer,
    save_method="merged_4bit"
)


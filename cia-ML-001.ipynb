{"cells": [{"cell_type": "markdown", "metadata": {"id": "V5CtDPO9vHq-"}, "source": ["**这都是啥啥啥和(‧_‧?)**"]}, {"cell_type": "markdown", "metadata": {"id": "U16hZ2_N-qYU"}, "source": ["**基础环境**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "OzGl3p_wzq30"}, "outputs": [], "source": ["%pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl==0.15.2 triton cut_cross_entropy unsloth_zoo\n", "%pip install sentencepiece protobuf datasets huggingface_hub hf_transfer\n", "%pip install  unsloth\n", "%pip install -U datasets"]}, {"cell_type": "markdown", "metadata": {"id": "h-Ihrgrw-w8N"}, "source": ["**加载自定义数据集**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "executionInfo": {"elapsed": 69183, "status": "ok", "timestamp": 1749086188907, "user": {"displayName": "None Null", "userId": "09114443515550358342"}, "user_tz": -480}, "id": "nJ86pgJn0pNT", "outputId": "690067cb-6f7c-48d6-f5b0-6664a06abc65"}, "outputs": [], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name=\"unsloth/Qwen3-0.6B-unsloth-bnb-4bit\",  # 80亿参数量化模型\n", "    max_seq_length=2048,               # 支持2048token上下文\n", "    # dtype = torch.float16,\n", "    load_in_4bit=True,                             # 4位量化降低内存占用\n", "    load_in_8bit=False,                            # 8位模式（需更高显存）\n", "    full_finetuning=False,                         # 启用参数高效微调（PEFT）\n", "    # token=\"<YOUR_HF_TOKEN>\",                    # 访问权限模型需提供令牌\n", ")\n", "\n", "model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r=32,                        # LoRA矩阵秩，值越大精度越高\n", "    target_modules=[             # 需适配的模型层\n", "        \"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "        \"gate_proj\", \"up_proj\", \"down_proj\"\n", "    ],\n", "    lora_alpha=64,               # 缩放因子，通常设为r的2倍\n", "    lora_dropout=0,              # 关闭 dropout\n", "    bias=\"none\",                 # 不微调偏置项\n", "    use_gradient_checkpointing= False,  # 支持长上下文\n", "    random_state=3433,           # 随机种子确保可复现\n", ")\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 136, "referenced_widgets": ["c3bce77ffa444faca383f5d3fdcf6c9f", "590e2954c52a4f5c979cdc9e6aca0964", "6f687c0b897a4444befec108fc32a786", "27a1f2ef8c4c42afb7e30c9323e7e04a", "dab865535d3942388a85b590dc0cabd3", "8bccdefcbdb7471e885406c24c1b1e83", "e0bf687d63c2493ea84ad25cc902bc97", "307382dfcf804c039a9490e109e3b97d", "4799d43eb34f41e4a12ca78373f9e471", "6d776208265d4b6a8a8cf8ca351b225c", "1fdd995e890740f4b4084ba3487744b9", "f1c97ce73a2b40d8ae672190b81c5f0a", "99a9af6c03de47b78fcc8e66bb5551a7", "5d6a2af95e48447c8c522c04c675cb6f", "fcca22c7d4604ddd9ba695510b45c703", "bb3eccb6b3e340788f378d0b684e3505", "6899f4e65a8c4647820092adee46bdb6", "40b601f9552e4f009e47470b5d270f88", "b369fde0b2fa4ed3b6d9c2573706b736", "cc1b16ded90847d3af7536009b3d3c89", "67ecb4c9e3a44d6d9d3531eea4fa65d8", "9a068509211c440ab6a9244a0c3e76cb"]}, "executionInfo": {"elapsed": 875, "status": "ok", "timestamp": 1749034235372, "user": {"displayName": "None Null", "userId": "09114443515550358342"}, "user_tz": -480}, "id": "6LhAL3Kx-whs", "outputId": "6c7c165a-2101-4a5b-cdcb-0fab5c54e08b"}, "outputs": [], "source": ["from datasets import load_dataset,Dataset\n", "import pandas as pd\n", "\n", "# 加载推理与对话数据集\n", "my_dataset = load_dataset(\"json\", data_files=\"./identity.json\")\n", "\n", " # 查看所有列名\n", "print(\"数据集列名:\", my_dataset.column_names)\n", "\n", "# 标准化推理数据为对话格式\n", "def generate_conversation(examples):\n", "    problems = examples[\"instruction\"]\n", "    solutions = examples[\"output\"]  \n", "    return {\n", "        \"conversations\": [\n", "            {\"role\": \"user\", \"content\": problems},{\"role\": \"assistant\", \"content\": solutions}\n", "        ]\n", "    }\n", "\n", "print(\"当前数据:\", my_dataset.map(generate_conversation)[\"train\"][0])\n", "\n", "my_conversations = tokenizer.apply_chat_template(\n", "    my_dataset.map(generate_conversation)[\"train\"][\"conversations\"],\n", "    tokenize=False\n", ")\n", "\n", "# 设定对话比例\n", "chat_percentage = 0.7\n", "data = pd.concat([\n", "    pd.Series(my_conversations)\n", "])\n", "data.name = \"text\"\n", "\n", "combined_dataset = Dataset.from_pandas(pd.DataFrame(data))\n", "combined_dataset = combined_dataset.shuffle(seed=3407)\n"]}, {"cell_type": "markdown", "metadata": {"id": "3BfEKDORLYm7"}, "source": ["**开始训练**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VXTQ1HEoLXo7"}, "outputs": [], "source": ["from trl import SFTTrainer, SFTConfig\n", "\n", "print(\"开始训练...\")\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    train_dataset=combined_dataset,  # 结构化数据集\n", "    eval_dataset=None,\n", "    args=SFTConfig(\n", "        dataset_text_field=\"text\",       # 用于训练的数据集字段\n", "        per_device_train_batch_size=2,   # 单设备训练批次大小\n", "        gradient_accumulation_steps=4,   # 梯度累积步数\n", "        warmup_steps=5,                  # 学习率预热步数\n", "        max_steps=30,                    # 总训练步数\n", "        learning_rate=2e-4,              # 学习率\n", "        logging_steps=1,                 # 日志记录频率\n", "        optim=\"adamw_8bit\",              # 优化器\n", "        weight_decay=0.01,               # 权重衰减\n", "        lr_scheduler_type=\"linear\",      # 学习率衰减策略\n", "        seed=3407,\n", "        report_to=\"none\",                # 日志平台（可选wandb）\n", "    ),\n", ")\n", "\n", "trainer_stats = trainer.train()"]}, {"cell_type": "markdown", "metadata": {"id": "GGNCR8n5Li2s"}, "source": ["**模型对话**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 8672, "status": "ok", "timestamp": 1749086208959, "user": {"displayName": "None Null", "userId": "09114443515550358342"}, "user_tz": -480}, "id": "dk8W83NJLl-c", "outputId": "259e11c1-36ca-4d8f-e36d-7bbddeb5819a"}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"user\", \"content\": \"你是谁！ 中文回答\"}\n", "]\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize=False,\n", "    add_generation_prompt=True,  # 生成响应必需\n", "    enable_thinking=False,    # 禁用思考\n", ")\n", "\n", "from transformers import TextStreamer\n", "_ = model.generate(\n", "    **tokenizer(text, return_tensors=\"pt\").to(\"cuda\"),\n", "    max_new_tokens=256,         # 最大生成token数\n", "    temperature=0.7, top_p=0.8, top_k=20,\n", "    streamer=TextStreamer(tokenizer, skip_prompt=True),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "ofHX8kMnejlI"}, "source": ["**模型本地保存**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MWiHz4WuejE5"}, "outputs": [], "source": ["model.save_pretrained_merged(\n", "    \"drive/MyDrive/Models/Qwen3-0.6B-unsloth-cia-finetuned\",\n", "    tokenizer,\n", "    save_method=\"merged_16bit\"\n", ")"]}], "metadata": {"accelerator": "GPU", "colab": {"authorship_tag": "ABX9TyNpfLrHfNYBhfSbBeLfhpdy", "gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"1fdd995e890740f4b4084ba3487744b9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "27a1f2ef8c4c42afb7e30c9323e7e04a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6d776208265d4b6a8a8cf8ca351b225c", "placeholder": "​", "style": "IPY_MODEL_1fdd995e890740f4b4084ba3487744b9", "value": " 91/0 [00:00&lt;00:00, 1479.63 examples/s]"}}, "307382dfcf804c039a9490e109e3b97d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "40b601f9552e4f009e47470b5d270f88": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4799d43eb34f41e4a12ca78373f9e471": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "590e2954c52a4f5c979cdc9e6aca0964": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8bccdefcbdb7471e885406c24c1b1e83", "placeholder": "​", "style": "IPY_MODEL_e0bf687d63c2493ea84ad25cc902bc97", "value": "Generating train split: "}}, "5d6a2af95e48447c8c522c04c675cb6f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b369fde0b2fa4ed3b6d9c2573706b736", "max": 91, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cc1b16ded90847d3af7536009b3d3c89", "value": 91}}, "67ecb4c9e3a44d6d9d3531eea4fa65d8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6899f4e65a8c4647820092adee46bdb6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d776208265d4b6a8a8cf8ca351b225c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f687c0b897a4444befec108fc32a786": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_307382dfcf804c039a9490e109e3b97d", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4799d43eb34f41e4a12ca78373f9e471", "value": 1}}, "8bccdefcbdb7471e885406c24c1b1e83": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "99a9af6c03de47b78fcc8e66bb5551a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6899f4e65a8c4647820092adee46bdb6", "placeholder": "​", "style": "IPY_MODEL_40b601f9552e4f009e47470b5d270f88", "value": "Map: 100%"}}, "9a068509211c440ab6a9244a0c3e76cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b369fde0b2fa4ed3b6d9c2573706b736": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bb3eccb6b3e340788f378d0b684e3505": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c3bce77ffa444faca383f5d3fdcf6c9f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_590e2954c52a4f5c979cdc9e6aca0964", "IPY_MODEL_6f687c0b897a4444befec108fc32a786", "IPY_MODEL_27a1f2ef8c4c42afb7e30c9323e7e04a"], "layout": "IPY_MODEL_dab865535d3942388a85b590dc0cabd3"}}, "cc1b16ded90847d3af7536009b3d3c89": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "dab865535d3942388a85b590dc0cabd3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e0bf687d63c2493ea84ad25cc902bc97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f1c97ce73a2b40d8ae672190b81c5f0a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_99a9af6c03de47b78fcc8e66bb5551a7", "IPY_MODEL_5d6a2af95e48447c8c522c04c675cb6f", "IPY_MODEL_fcca22c7d4604ddd9ba695510b45c703"], "layout": "IPY_MODEL_bb3eccb6b3e340788f378d0b684e3505"}}, "fcca22c7d4604ddd9ba695510b45c703": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_67ecb4c9e3a44d6d9d3531eea4fa65d8", "placeholder": "​", "style": "IPY_MODEL_9a068509211c440ab6a9244a0c3e76cb", "value": " 91/91 [00:00&lt;00:00, 3044.10 examples/s]"}}}}}, "nbformat": 4, "nbformat_minor": 0}
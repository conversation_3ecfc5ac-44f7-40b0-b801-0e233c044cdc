#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Harbor Repository Client - Python Implementation
重构自 tools/repository.go 的 Python 版本
"""

import requests
from urllib.parse import quote
from http import HTTPStatus

# 常量定义
CICD_PROJECT = "devops-"


class Repository:
    """Harbor 仓库客户端 - 对应 Go 版本的 repository struct"""

    def __init__(self, host: str, auth_name: str, auth_psw: str):
        """
        初始化 Harbor 仓库客户端

        Args:
            host: Harbor 服务器地址
            auth_name: 认证用户名 (对应 Go 版本的 AuthName)
            auth_psw: 认证密码 (对应 Go 版本的 AuthPsw)
        """
        self.host = host.rstrip('/')
        self.auth_name = auth_name
        self.auth_psw = auth_psw
        self.session = requests.Session()
        self.session.auth = (auth_name, auth_psw)

    def delete(self, project_name: str, repository_name: str) -> None:
        """
        删除指定的仓库 - 对应 Go 版本的 Delete 方法

        Args:
            project_name: 项目名称
            repository_name: 仓库名称
        """
        encoded_repo = quote(quote(repository_name, safe=''), safe='')
        url_path = f"{self.host}/api/v2.0/projects/{project_name}/repositories/{encoded_repo}"
        print(url_path)  # 对应 Go 版本的 println(urlPath)
        response = self.session.get(url_path, timeout=30)
        if response.status_code == HTTPStatus.NOT_FOUND:
            return
        if response.status_code != HTTPStatus.OK:
            raise Exception(f"GET repository, got {response.status_code} {response.reason}")
        
        # 2. 获取 CSRF Token
        csrf_token = response.headers.get('X-Harbor-Csrf-Token')
        # 3. 发送 DELETE 请求
        delete_headers = {}
        if csrf_token:
            delete_headers['X-Harbor-Csrf-Token'] = csrf_token

        delete_response = self.session.delete(
            url_path,
            headers=delete_headers,
            timeout=30
        )

        # 检查 DELETE 请求是否成功
        if delete_response.status_code != HTTPStatus.OK:
            raise Exception(f"DELETE repository, got {delete_response.status_code} {delete_response.reason}")

    def list_repositories(self, project_name: str, page: int = 1, page_size: int = 10) -> dict:
        """
        获取项目中的仓库列表

        Args:
            project_name: 项目名称
            page: 页码，从1开始
            page_size: 每页大小

        Returns:
            包含仓库列表和分页信息的字典
        """
        url_path = f"{self.host}/api/v2.0/projects/{project_name}/repositories"
        params = {
            'page': page,
            'page_size': page_size
        }
        response = self.session.get(url_path, params=params, timeout=30)
        if response.status_code != HTTPStatus.OK:
            raise Exception(f"GET repositories, got {response.status_code} {response.reason}")

        repositories = response.json()
        return repositories


def new_repository(host: str, auth_name: str, auth_psw: str) -> Repository:
    """
    创建新的 Repository 实例 - 对应 Go 版本的 NewRepository 函数

    Args:
        host: Harbor 服务器地址
        auth_name: 认证用户名
        auth_psw: 认证密码

    Returns:
        Repository 实例
    """
    return Repository(host, auth_name, auth_psw)
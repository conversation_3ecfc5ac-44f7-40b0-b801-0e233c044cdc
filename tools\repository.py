#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Harbor Repository Client - Python Implementation
重构自 tools/repository.go 的 Python 版本

功能：
- 连接 Harbor 镜像仓库
- 删除指定项目中的镜像仓库
- 支持基本认证
- 处理 CSRF Token
"""

import requests
from urllib.parse import quote
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 常量定义
CICD_PROJECT = "devops-"


class HarborRepositoryError(Exception):
    """Harbor 仓库操作异常"""
    pass


class Repository:
    """Harbor 仓库客户端 - 对应 Go 版本的 repository struct"""

    def __init__(self, host: str, auth_name: str, auth_psw: str):
        """
        初始化 Harbor 仓库客户端

        Args:
            host: Harbor 服务器地址 (例如: https://harbor.example.com)
            auth_name: 认证用户名 (对应 Go 版本的 AuthName)
            auth_psw: 认证密码 (对应 Go 版本的 AuthPsw)
        """
        self.host = host.rstrip('/')  # 移除末尾的斜杠
        self.auth_name = auth_name
        self.auth_psw = auth_psw
        self.session = requests.Session()
        self.session.auth = (auth_name, auth_psw)

        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Harbor-Python-Client/1.0'
        })

        logger.info(f"初始化 Harbor 客户端: {host}")

    def delete(self, project_name: str, repository_name: str) -> None:
        """
        删除指定的仓库 - 对应 Go 版本的 Delete 方法

        Args:
            project_name: 项目名称
            repository_name: 仓库名称

        Raises:
            HarborRepositoryError: 当删除操作失败时
        """
        # 构建 URL，使用双重 URL 编码（与 Go 版本保持一致）
        encoded_repo = quote(quote(repository_name, safe=''), safe='')
        url_path = f"{self.host}/api/v2.0/projects/{project_name}/repositories/{encoded_repo}"

        print(url_path)  # 对应 Go 版本的 println(urlPath)

        try:
            # 1. 发送 GET 请求获取 CSRF Token
            response = self.session.get(url_path, timeout=30)

            # 如果仓库不存在，直接返回
            if response.status_code == 404:
                logger.info(f"仓库不存在: {project_name}/{repository_name}")
                return

            # 检查 GET 请求是否成功
            if response.status_code != 200:
                raise HarborRepositoryError(
                    f"GET repository, got {response.status_code} {response.reason}"
                )

            # 2. 获取 CSRF Token
            csrf_token = response.headers.get('X-Harbor-Csrf-Token')

            # 3. 发送 DELETE 请求
            delete_headers = {}
            if csrf_token:
                delete_headers['X-Harbor-Csrf-Token'] = csrf_token

            delete_response = self.session.delete(
                url_path,
                headers=delete_headers,
                timeout=30
            )

            # 检查 DELETE 请求是否成功
            if delete_response.status_code != 200:
                raise HarborRepositoryError(
                    f"DELETE repository, got {delete_response.status_code} {delete_response.reason}"
                )

            logger.info(f"成功删除仓库: {project_name}/{repository_name}")

        except requests.RequestException as e:
            raise HarborRepositoryError(f"网络请求失败: {str(e)}")

    def repository_exists(self, project_name: str, repository_name: str) -> bool:
        """
        检查仓库是否存在

        Args:
            project_name: 项目名称
            repository_name: 仓库名称

        Returns:
            存在返回 True，不存在返回 False
        """
        try:
            encoded_repo = quote(quote(repository_name, safe=''), safe='')
            url_path = f"{self.host}/api/v2.0/projects/{project_name}/repositories/{encoded_repo}"

            response = self.session.get(url_path, timeout=30)
            return response.status_code == 200

        except requests.RequestException:
            return False

    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()
            logger.info("Harbor 客户端会话已关闭")


def new_repository(host: str, auth_name: str, auth_psw: str) -> Repository:
    """
    创建新的 Repository 实例 - 对应 Go 版本的 NewRepository 函数

    Args:
        host: Harbor 服务器地址
        auth_name: 认证用户名
        auth_psw: 认证密码

    Returns:
        Repository 实例
    """
    return Repository(host, auth_name, auth_psw)


# 使用示例和测试代码
if __name__ == "__main__":
    # 示例配置
    HARBOR_HOST = "https://harbor.example.com"
    USERNAME = "admin"
    PASSWORD = "Harbor12345"

    # 创建客户端 - 使用与 Go 版本相同的函数名
    repo_client = new_repository(HARBOR_HOST, USERNAME, PASSWORD)

    try:
        # 示例：删除仓库
        project = "my-project"
        repository = "my-app"

        print(f"尝试删除仓库: {project}/{repository}")

        # 检查仓库是否存在
        if repo_client.repository_exists(project, repository):
            print(f"仓库 {project}/{repository} 存在，开始删除...")

            # 删除仓库 - 使用与 Go 版本相同的方法名
            repo_client.delete(project, repository)
            print(f"删除操作完成")
        else:
            print(f"仓库 {project}/{repository} 不存在")

    except HarborRepositoryError as e:
        logger.error(f"Harbor 操作失败: {e}")
    except Exception as e:
        logger.error(f"未预期的错误: {e}")
    finally:
        repo_client.close()


# 额外的工具函数
def delete_repository_batch(host: str, auth_name: str, auth_psw: str,
                           repositories: list) -> dict:
    """
    批量删除仓库

    Args:
        host: Harbor 服务器地址
        auth_name: 认证用户名
        auth_psw: 认证密码
        repositories: 仓库列表，格式为 [{"project": "proj1", "repo": "repo1"}, ...]

    Returns:
        删除结果字典，包含成功和失败的仓库列表
    """
    client = new_repository(host, auth_name, auth_psw)
    results = {
        "success": [],
        "failed": [],
        "not_found": []
    }

    try:
        for repo_info in repositories:
            project = repo_info.get("project")
            repository = repo_info.get("repo")

            if not project or not repository:
                results["failed"].append({
                    "project": project,
                    "repo": repository,
                    "error": "项目名或仓库名为空"
                })
                continue

            try:
                if not client.repository_exists(project, repository):
                    results["not_found"].append({
                        "project": project,
                        "repo": repository
                    })
                    continue

                client.delete(project, repository)
                results["success"].append({
                    "project": project,
                    "repo": repository
                })

            except HarborRepositoryError as e:
                results["failed"].append({
                    "project": project,
                    "repo": repository,
                    "error": str(e)
                })

    finally:
        client.close()

    return results


def is_cicd_project(project_name: str) -> bool:
    """
    检查是否为 CI/CD 项目 - 使用 CICD_PROJECT 常量

    Args:
        project_name: 项目名称

    Returns:
        如果是 CI/CD 项目返回 True
    """
    return project_name.startswith(CICD_PROJECT)
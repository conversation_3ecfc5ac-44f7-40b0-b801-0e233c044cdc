# -*- coding: utf-8 -*-
"""
Created on Fri Jan 26 17:17:23 2018

@author: sty
"""

from flask import Flask
from flask_restful import reqparse, abort, Api, Resource

app = Flask(__name__)
api = Api(app)


class Staff(Resource):
    def get(self):
        return ""
    
class Company(Resource):
    def get(self):
        return ""



##
## Actually setup the Api resource routing here
##
api.add_resource(Staff, '/s')
api.add_resource(Company, '/c')


if __name__ == '__main__':
    app.run(host="127.0.0.1", port=3690,debug=True)

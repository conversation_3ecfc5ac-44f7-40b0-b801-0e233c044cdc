apiVersion: core.oam.dev/v1beta1
kind: Application
metadata:
  name: vecd
  namespace: media
spec:
  components:
    - name: vecd
      type: general
      properties:
        name: vecd
        appName: vecd
        containerName: vecd
        image: cr.ttyuyin.com/zt/vecd:V20230420103659-release-vecd_alter-5f8deead5
        networkSettings:
          type: ClusterIP
          ports:
            - containerPort: 8000
              servicePort: 8000
              name: grpc-8000
        commandArgs:
          - /app/vecd
          - --config=/app/config/vecd.json
          - --with-name-registration=false
          - --listen=:8000
          - --log_level=info
        env:
          - name: MY_APP_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.labels['app']
          - name: MY_NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          - name: MY_POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: MY_POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: MY_POD_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.podIP
          - name: MY_POD_SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.serviceAccountName
          - name: POD_CPU_REQUEST
            valueFrom:
              resourceFieldRef:
                containerName: vecd
                divisor: "1m"
                resource: requests.cpu
          - name: POD_CPU_LIMIT
            valueFrom:
              resourceFieldRef:
                containerName: vecd
                divisor: "1m"
                resource: limits.cpu
          - name: POD_MEM_REQUEST
            valueFrom:
              resourceFieldRef:
                containerName: vecd
                divisor: "1Mi"
                resource: requests.memory
          - name: POD_MEM_LIMIT
            valueFrom:
              resourceFieldRef:
                containerName: vecd
                divisor: "1Mi"
                resource: limits.memory
          - name: MY_IMAGE_VERSION
            value: V20230420103659-release-vecd_alter-5f8deead5
          - name: TT_Sentinel_CD_AT
            value: 2025-06-30 15:22:26.684659
        vipConfigurations:
          vecd.json: |
                    {
                      "event_kafka": {
                        "brokers":   "************:9092,************:9092,************:9092",
                        "topics":    "vecd_index_event",
                        "group_id":  "vecd_index_event",
                        "client_id": "vecd_index_event"
                      },
                      "log_kafka":{
                        "brokers":   "************:9092,************:9092,************:9092",
                        "topics":    "vecd_log",
                        "group_id":  "vecd_log",
                        "client_id": "vecd_log",
                        "consume_all" : true
                      },
                      "redis":{
                        "host": "*************",
                        "port": 6379,
                        "read_timeout": 2,
                        "write_timeout": 2,
                        "pool_timeout": 2,
                        "pool_size": 500
                      },
                      "dump_index":{
                        "interval":180
                      },
                	"server":{
                      "env":"prod"
                    },
                    "feishu_robot_url":"https://open.feishu.cn/open-apis/bot/v2/hook/bdf96acd-a62f-4dca-8a6c-2b87bf44f56e",
                    "obs_index_dir":"/vecd-data/media"
                    }
        mchLabels:
          app: vecd
          app.kubernetes.io/instance: vecd
          app.kubernetes.io/name: vecd
        serSltLabels:
          app.kubernetes.io/instance: vecd
          app.kubernetes.io/name: vecd
        platformLabels:
          app: vecd
          release: vecd
          senv: base
          uuid: 6348d672aaa3c5a9980fd91e
        vipMountPath: /app/config
        labels:
          senv: base
          type: app
          lang: go
          uuid: 6348d672aaa3c5a9980fd91e
          env: PRODUCTION
        annotations:
          mpe/flow-control-repo: rcmd
        serviceAccount: default
        priorityClassName: prod-p1        
        healthCheck:
          - livenessProbe:
              exec:
                command:
                  - grpc_health_probe
                  - -addr=127.0.0.1:8000
                  - -rpc-timeout=2s
              initialDelaySeconds: 30
              periodSeconds: 5
              timeoutSeconds: 2
              successThreshold: 1
              failureThreshold: 3
          - readinessProbe:
              exec:
                command:
                  - grpc_health_probe
                  - -addr=127.0.0.1:8000
                  - -rpc-timeout=2s
              initialDelaySeconds: 20
              periodSeconds: 10
              timeoutSeconds: 0
              successThreshold: 1
              failureThreshold: 3                
        volumeMounts:
          configMap:
            - cmName: service-targets-config
              name: service-targets-config
              mountPath: /root/etc/client/
              defaultMode: 438
          hostPath:
            - path: /etc/localtime
              name: host-time
              mountPath: /etc/localtime
              defaultMode: 438
      traits:
        - type: topologyspreadconstraints
          properties:
            constraints:
            - topologyKey: topology.kubernetes.io/zone
              maxSkew: 1
              whenUnsatisfiable: ScheduleAnyway
              labelSelector:
                matchLabels:
                  app: vecd        
        - type: scaler
          properties:
            replicas: 1        
        - type: monitor
          properties:
            port: 2112
            metrics_path: /metrics
            container_name: service                
        - type: tt-resource
          properties:
            containerName: vecd
            requests:
              cpu: "2"
              memory: 2048Mi
            limits:
              cpu: "4"
              memory: 4096Mi 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Harbor Repository Client 测试脚本
测试重构后的 Python 版本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from repository import new_repository


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")

    # 测试配置（请根据实际情况修改）
    HARBOR_HOST = "https://harbor.example.com"
    USERNAME = "admin"
    PASSWORD = "Harbor12345"

    # 创建客户端
    print(f"1. 创建 Harbor 客户端...")
    repo_client = new_repository(HARBOR_HOST, USERNAME, PASSWORD)

    try:
        # 测试删除功能
        print("2. 测试删除功能...")
        test_project = "test-project"
        test_repo = "test-app"

        print(f"   尝试删除仓库: {test_project}/{test_repo}")
        repo_client.delete(test_project, test_repo)
        print("   删除操作完成")

    except Exception as e:
        print(f"   操作失败: {e}")

    print("3. 测试完成")


def main():
    """主测试函数"""
    print("Harbor Repository Client Python 版本测试")
    print("=" * 50)

    # 运行测试
    test_basic_functionality()

    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Harbor Repository Client 测试脚本
测试重构后的 Python 版本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from repository import new_repository


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")

    # 测试配置（请根据实际情况修改）
    HARBOR_HOST = "https://harbor.example.com"
    USERNAME = "admin"
    PASSWORD = "Harbor12345"

    # 创建客户端
    print(f"1. 创建 Harbor 客户端...")
    repo_client = new_repository(HARBOR_HOST, USERNAME, PASSWORD)

    try:
        # 测试删除功能
        print("2. 测试删除功能...")
        test_project = "test-project"
        test_repo = "test-app"

        print(f"   尝试删除仓库: {test_project}/{test_repo}")
        repo_client.delete(test_project, test_repo)
        print("   删除操作完成")

    except Exception as e:
        print(f"   操作失败: {e}")

    print("3. 测试完成")


def test_list_repositories():
    """测试获取仓库列表功能"""
    print("\n=== 测试获取仓库列表功能 ===")

    # 测试配置
    HARBOR_HOST = "https://cr.ttyuyin.com"
    USERNAME = "admin"
    PASSWORD = "Harbor12345"
    PROJECT_NAME = "devops"

    # 创建客户端
    print(f"1. 创建 Harbor 客户端...")
    repo_client = new_repository(HARBOR_HOST, USERNAME, PASSWORD)

    try:
        print(f"2. 分页获取项目 '{PROJECT_NAME}' 的所有仓库...")

        all_repositories = []
        page = 1
        page_size = 10

        while True:
            print(f"   正在获取第 {page} 页...")

            try:
                result = repo_client.list_repositories(PROJECT_NAME, page, page_size)
                repositories = result['repositories']
                total_count = result['total_count']

                print(f"   第 {page} 页: 获取到 {len(repositories)} 个仓库")
                print(f"   总数: {total_count}")

                # 添加到总列表
                all_repositories.extend(repositories)

                # 打印仓库名称
                for repo in repositories:
                    print(f"     - {repo['name']}")

                # 检查是否还有更多页
                if len(repositories) < page_size:
                    print("   已获取所有仓库")
                    break

                page += 1

            except Exception as e:
                print(f"   获取第 {page} 页失败: {e}")
                break

        print(f"\n3. 获取完成，共找到 {len(all_repositories)} 个仓库:")
        for i, repo in enumerate(all_repositories, 1):
            print(f"   {i}. {repo['name']} (ID: {repo['id']}, 制品数: {repo['artifact_count']})")

    except Exception as e:
        print(f"   操作失败: {e}")

    print("4. 测试完成")


def main():
    """主测试函数"""
    print("Harbor Repository Client Python 版本测试")
    print("=" * 50)

    # 运行测试
    test_basic_functionality()
    test_list_repositories()

    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()

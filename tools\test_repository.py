#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Harbor Repository Client 测试脚本
测试重构后的 Python 版本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from repository import new_repository, delete_repository_batch, is_cicd_project, HarborRepositoryError


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 测试配置（请根据实际情况修改）
    HARBOR_HOST = "https://harbor.example.com"
    USERNAME = "admin"
    PASSWORD = "Harbor12345"
    
    # 创建客户端
    print(f"1. 创建 Harbor 客户端...")
    repo_client = new_repository(HARBOR_HOST, USERNAME, PASSWORD)
    
    try:
        # 测试仓库存在性检查
        print("2. 测试仓库存在性检查...")
        test_project = "test-project"
        test_repo = "test-app"
        
        exists = repo_client.repository_exists(test_project, test_repo)
        print(f"   仓库 {test_project}/{test_repo} 存在: {exists}")
        
        # 如果仓库存在，尝试删除
        if exists:
            print("3. 尝试删除仓库...")
            repo_client.delete(test_project, test_repo)
            print("   删除操作完成")
        else:
            print("3. 仓库不存在，跳过删除操作")
            
    except HarborRepositoryError as e:
        print(f"   Harbor 操作失败: {e}")
    except Exception as e:
        print(f"   未预期的错误: {e}")
    finally:
        repo_client.close()
        print("4. 客户端会话已关闭")


def test_batch_delete():
    """测试批量删除功能"""
    print("\n=== 测试批量删除功能 ===")
    
    # 测试配置
    HARBOR_HOST = "https://harbor.example.com"
    USERNAME = "admin"
    PASSWORD = "Harbor12345"
    
    # 测试仓库列表
    repositories = [
        {"project": "test-project1", "repo": "app1"},
        {"project": "test-project1", "repo": "app2"},
        {"project": "test-project2", "repo": "service1"},
        {"project": "", "repo": "invalid"},  # 无效项目名
        {"project": "test-project3", "repo": ""},  # 无效仓库名
    ]
    
    print(f"1. 批量删除 {len(repositories)} 个仓库...")
    results = delete_repository_batch(HARBOR_HOST, USERNAME, PASSWORD, repositories)
    
    print("2. 删除结果:")
    print(f"   成功: {len(results['success'])} 个")
    for item in results['success']:
        print(f"     - {item['project']}/{item['repo']}")
    
    print(f"   未找到: {len(results['not_found'])} 个")
    for item in results['not_found']:
        print(f"     - {item['project']}/{item['repo']}")
    
    print(f"   失败: {len(results['failed'])} 个")
    for item in results['failed']:
        print(f"     - {item['project']}/{item['repo']}: {item['error']}")


def test_cicd_project_check():
    """测试 CI/CD 项目检查功能"""
    print("\n=== 测试 CI/CD 项目检查功能 ===")
    
    test_projects = [
        "devops-frontend",
        "devops-backend", 
        "production-app",
        "test-service",
        "devops-",
        "my-devops-project"
    ]
    
    print("项目名称检查结果:")
    for project in test_projects:
        is_cicd = is_cicd_project(project)
        print(f"   {project}: {'是' if is_cicd else '不是'} CI/CD 项目")


def test_url_encoding():
    """测试 URL 编码功能"""
    print("\n=== 测试 URL 编码功能 ===")
    
    from urllib.parse import quote
    
    test_repos = [
        "simple-app",
        "app/with/slashes",
        "app-with-special@chars",
        "app with spaces",
        "中文仓库名"
    ]
    
    print("URL 编码测试:")
    for repo in test_repos:
        single_encoded = quote(repo, safe='')
        double_encoded = quote(quote(repo, safe=''), safe='')
        print(f"   原始: {repo}")
        print(f"   单次编码: {single_encoded}")
        print(f"   双次编码: {double_encoded}")
        print()


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 使用无效的 Harbor 地址
    INVALID_HOST = "https://invalid-harbor.example.com"
    USERNAME = "admin"
    PASSWORD = "password"
    
    repo_client = new_repository(INVALID_HOST, USERNAME, PASSWORD)
    
    try:
        print("1. 测试连接到无效的 Harbor 服务器...")
        exists = repo_client.repository_exists("test", "app")
        print(f"   结果: {exists}")
    except Exception as e:
        print(f"   预期的错误: {type(e).__name__}: {e}")
    finally:
        repo_client.close()


def main():
    """主测试函数"""
    print("Harbor Repository Client Python 版本测试")
    print("=" * 50)
    
    # 运行各项测试
    test_basic_functionality()
    test_batch_delete()
    test_cicd_project_check()
    test_url_encoding()
    test_error_handling()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n注意：")
    print("- 请根据实际的 Harbor 服务器配置修改测试参数")
    print("- 某些测试可能因为网络连接或认证问题而失败")
    print("- 在生产环境中使用前请充分测试")


if __name__ == "__main__":
    main()
